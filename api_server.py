"""
REST API Server for MIT CVD App
Exposes all functionality through REST endpoints for external clients
"""
from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import uvicorn
import os
import logging

# Import all modules
import crud_module
import chat_module
import audio_module
import vision_module
import cvd_score_module
import heuristic_module
import gamification_module
import pdf_module
from config import OPENAI_API_KEY

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="MIT CVD App API",
    description="Comprehensive cardiovascular health assessment platform API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class UserCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    age: int = Field(..., ge=0, le=150)
    email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')

class UserUpdate(BaseModel):
    profile: Optional[Dict[str, Any]] = None

class ChatMessage(BaseModel):
    message: str = Field(..., min_length=1)
    user_id: Optional[str] = None

class HealthResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str

# Health check endpoint
@app.get("/health", response_model=Dict[str, Any])
async def health_check():
    """Health check endpoint"""
    try:
        db_health = crud_module.get_database_health()
        return {
            "status": "healthy",
            "api_version": "1.0.0",
            "database": db_health,
            "openai_configured": bool(OPENAI_API_KEY),
            "modules_loaded": True
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Service unhealthy")

# User management endpoints
@app.post("/users", response_model=HealthResponse)
async def create_user(user: UserCreate):
    """Create a new user"""
    try:
        new_user = crud_module.create_user(user.name, user.age, user.email)
        if new_user:
            return HealthResponse(
                success=True,
                data=new_user,
                timestamp=crud_module.current_timestamp()
            )
        else:
            raise HTTPException(status_code=400, detail="Failed to create user")
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/users", response_model=HealthResponse)
async def list_users():
    """List all users"""
    try:
        users = crud_module.list_users()
        return HealthResponse(
            success=True,
            data={"users": users, "count": len(users)},
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error listing users: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/users/{user_id}", response_model=HealthResponse)
async def get_user(user_id: str):
    """Get user by ID"""
    try:
        user = crud_module.read_user(user_id)
        if user:
            return HealthResponse(
                success=True,
                data=user,
                timestamp=crud_module.current_timestamp()
            )
        else:
            raise HTTPException(status_code=404, detail="User not found")
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.put("/users/{user_id}", response_model=HealthResponse)
async def update_user(user_id: str, updates: UserUpdate):
    """Update user data"""
    try:
        success = crud_module.update_user(user_id, updates.dict(exclude_unset=True))
        if success:
            updated_user = crud_module.read_user(user_id)
            return HealthResponse(
                success=True,
                data=updated_user,
                timestamp=crud_module.current_timestamp()
            )
        else:
            raise HTTPException(status_code=400, detail="Failed to update user")
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Chat endpoints
@app.post("/chat", response_model=HealthResponse)
async def chat_with_ai(message: ChatMessage):
    """Chat with AI health assistant"""
    try:
        user_context = None
        if message.user_id:
            user_context = crud_module.read_user(message.user_id)

        response = chat_module.get_insights(message.message, user_context)
        return HealthResponse(
            success=response['success'],
            data=response,
            error=response.get('error'),
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/users/{user_id}/sessions", response_model=HealthResponse)
async def get_user_sessions(user_id: str):
    """Get all chat sessions for a user"""
    try:
        user = crud_module.read_user(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        sessions = crud_module.get_user_sessions(user_id)
        return HealthResponse(
            success=True,
            data={"sessions": sessions, "count": len(sessions)},
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error getting sessions for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions/{session_id}", response_model=HealthResponse)
async def get_session(session_id: str):
    """Get specific session by ID"""
    try:
        session = crud_module.read_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        return HealthResponse(
            success=True,
            data=session,
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error getting session {session_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Audio processing endpoints
@app.post("/audio/transcribe", response_model=HealthResponse)
async def transcribe_audio(
    audio_file: UploadFile = File(...),
    user_id: Optional[str] = Form(None),
    health_processing: bool = Form(True)
):
    """Transcribe audio file to text"""
    try:
        # Validate file type
        if not audio_file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")
        
        # Save uploaded file
        audio_data = await audio_file.read()
        temp_path = audio_module.save_uploaded_audio(audio_data, audio_file.filename)
        
        try:
            # Process audio
            if health_processing:
                result = audio_module.transcribe_health_audio(temp_path)
            else:
                result = audio_module.transcribe_audio(temp_path)
            
            return HealthResponse(
                success=result['success'],
                data=result,
                error=result.get('error'),
                timestamp=crud_module.current_timestamp()
            )
        finally:
            # Clean up temporary file
            audio_module.cleanup_temp_audio_file(temp_path)
            
    except Exception as e:
        logger.error(f"Error transcribing audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Computer vision endpoints
@app.post("/vision/analyze-food", response_model=HealthResponse)
async def analyze_food_image(
    image_file: UploadFile = File(...),
    user_id: Optional[str] = Form(None)
):
    """Analyze food image for nutritional content"""
    try:
        # Validate file type
        if not image_file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image file")
        
        # Save uploaded file
        image_data = await image_file.read()
        temp_path = vision_module.save_uploaded_image(image_data, image_file.filename)
        
        try:
            # Get user context if provided
            user_context = None
            if user_id:
                user_context = crud_module.read_user(user_id)
            
            # Process image
            result = vision_module.process_food_image(temp_path, user_context)
            
            # Log nutrition data if user provided and analysis successful
            if user_id and result.get('success'):
                nutrition_log = {
                    'name': result.get('food_name', 'Unknown'),
                    'nutrition': result.get('nutrition', {}),
                    'image_path': temp_path
                }
                crud_module.add_nutrition_log_to_user(user_id, nutrition_log)
            
            return HealthResponse(
                success=result['success'],
                data=result,
                error=result.get('error'),
                timestamp=crud_module.current_timestamp()
            )
        finally:
            # Clean up temporary file
            vision_module.cleanup_temp_image_file(temp_path)
            
    except Exception as e:
        logger.error(f"Error analyzing food image: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# CVD assessment endpoints
@app.post("/cvd/assess/{user_id}", response_model=HealthResponse)
async def assess_cvd_risk(user_id: str):
    """Calculate CVD risk for user"""
    try:
        user_data = crud_module.read_user(user_id)
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
        
        cvd_result = cvd_score_module.calculate_cvd_risk(user_data)
        
        if cvd_result['success']:
            # Save CVD score to user
            crud_module.add_cvd_score_to_user(user_id, cvd_result['score_data'])
        
        return HealthResponse(
            success=cvd_result['success'],
            data=cvd_result,
            error=cvd_result.get('error'),
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error assessing CVD risk for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Heuristic analysis endpoints
@app.post("/heuristic/analyze/{user_id}", response_model=HealthResponse)
async def analyze_heuristic(user_id: str):
    """Perform heuristic analysis for user"""
    try:
        user_data = crud_module.read_user(user_id)
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
        
        heuristic_result = heuristic_module.calculate_heuristic(user_data)
        
        return HealthResponse(
            success=heuristic_result['success'],
            data=heuristic_result,
            error=heuristic_result.get('error'),
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error analyzing heuristic for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Gamification endpoints
@app.get("/gamification/progress/{user_id}", response_model=HealthResponse)
async def get_user_progress(user_id: str):
    """Get gamification progress for user"""
    try:
        progress = gamification_module.get_user_progress(user_id)
        return HealthResponse(
            success=progress['success'],
            data=progress,
            error=progress.get('error'),
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error getting progress for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gamification/checkin/{user_id}", response_model=HealthResponse)
async def daily_checkin(user_id: str):
    """Perform daily check-in for user"""
    try:
        checkin_result = gamification_module.quick_checkin(user_id)
        return HealthResponse(
            success=checkin_result['success'],
            data=checkin_result,
            error=checkin_result.get('error'),
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error performing check-in for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/gamification/activity/{user_id}", response_model=HealthResponse)
async def log_activity(user_id: str, activity_type: str = Form(...)):
    """Log activity for user"""
    try:
        result = gamification_module.log_activity(user_id, activity_type)
        return HealthResponse(
            success=result['success'],
            data=result,
            error=result.get('error'),
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error logging activity for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# PDF report endpoints
@app.post("/reports/generate/{user_id}")
async def generate_report(user_id: str, report_type: str = Form("comprehensive")):
    """Generate PDF report for user"""
    try:
        user_data = crud_module.read_user(user_id)
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")

        pdf_result = pdf_module.export_user_report(user_data, report_type)

        if pdf_result['success']:
            return FileResponse(
                path=pdf_result['filepath'],
                filename=pdf_result['filename'],
                media_type='application/pdf'
            )
        else:
            raise HTTPException(status_code=500, detail=pdf_result.get('error', 'PDF generation failed'))

    except Exception as e:
        logger.error(f"Error generating report for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Database management endpoints
@app.get("/admin/database/health", response_model=HealthResponse)
async def get_database_health():
    """Get database health information (admin endpoint)"""
    try:
        health_info = crud_module.get_database_health()
        return HealthResponse(
            success=True,
            data=health_info,
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error getting database health: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/admin/database/backup", response_model=HealthResponse)
async def backup_database():
    """Create database backup (admin endpoint)"""
    try:
        success = crud_module.backup_database()
        return HealthResponse(
            success=success,
            data={"backup_created": success},
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error creating database backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# System information endpoints
@app.get("/system/info", response_model=HealthResponse)
async def get_system_info():
    """Get system information"""
    try:
        transcription_methods = audio_module.get_available_transcription_methods()

        system_info = {
            "api_version": "1.0.0",
            "openai_configured": bool(OPENAI_API_KEY),
            "transcription_methods": transcription_methods,
            "modules": {
                "crud": True,
                "chat": True,
                "audio": True,
                "vision": True,
                "cvd_score": True,
                "heuristic": True,
                "gamification": True,
                "pdf": True
            }
        }

        return HealthResponse(
            success=True,
            data=system_info,
            timestamp=crud_module.current_timestamp()
        )
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"success": False, "error": "Endpoint not found", "timestamp": crud_module.current_timestamp()}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"success": False, "error": "Internal server error", "timestamp": crud_module.current_timestamp()}
    )

# Main function to run the server
def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Run the FastAPI server"""
    uvicorn.run("api_server:app", host=host, port=port, reload=reload)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="MIT CVD App API Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")

    args = parser.parse_args()

    print(f"🏥 Starting MIT CVD App API Server on {args.host}:{args.port}")
    print(f"📚 API Documentation: http://{args.host}:{args.port}/docs")
    print(f"📖 ReDoc Documentation: http://{args.host}:{args.port}/redoc")

    run_server(args.host, args.port, args.reload)
